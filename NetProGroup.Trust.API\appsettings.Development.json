{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Verbose",
      "Override": {
        "System": "Error",
        "Microsoft": "Warning",
        "Microsoft.AspNetCore": "Warning",
        "Microsoft.AspNetCore.Mvc.Infrastructure": "Information",
        "Microsoft.AspNetCore.Server": "Information",
        "Microsoft.EntityFrameworkCore": "Warning",
        "Microsoft.EntityFrameworkCore.Database.Command": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "restrictedToMinimumLevel": "Verbose"
        }
      }
    ]
  },
  "ConnectionStrings": {
    "Default": "Server=localhost;Database=TridentTrust;MultipleActiveResultSets=true;TrustServerCertificate=True;Trusted_Connection=True;",
    "MongoDb": "mongodb://localhost:27017"
  },
  "MongoDb": {
    "DatabaseName": "tnevcorptaxreturndb"
  },
  // Identifies the API to authenticate incoming authentication requests
  "AzureAd": {
    "Instance": "https://login.microsoftonline.com/",
    "TenantId": "30350f35-feb0-4bbc-877f-43ff406a41e5",
    "ClientId": "bdcf01f6-a196-478c-b778-7cbf57f19e5f",
    "AllowWebApiToBeAuthorizedByACL": true,
    "Audience": "api://bdcf01f6-a196-478c-b778-7cbf57f19e5f"
  },
  "AppRegistration": {
    "TenantId": "30350f35-feb0-4bbc-877f-43ff406a41e5",
    "ClientId": "bdcf01f6-a196-478c-b778-7cbf57f19e5f",
    "ClientSecret": "****************************************"
  },
  "ExternalId": {
    "ClientId": "fd02e740-f5dc-4e7a-9e4f-d35cd1af2251",
    "TenantId": "4053da93-8216-46fd-a82a-a32155693958"
  },
  "BlobStorage": {
    "AccountName": "sadevpcpeus2",
    "ContainerName": "documents"
  },
  "Azure": {
    "MSGraph": {
      "AD": {
        // Use defaults from AppRegistration
        "Scopes": ".default"
      }
    }
  },
  "Smtp": {
    "host": "smtp.sendgrid.net",
    "port": 587,
    "username": "apikey",
    //"password": "*********************************************************************", //uncomment if you need to test email sending
    "fromEmail": "<EMAIL>",
    "nameEmail": "NetPro Dev"
  },
  "Diagnose": true,
  "SwaggerConfig": {
    // Configure the base URL for this API, to be used in swagger for the token url.
    "ApplicationUrl": "https://localhost:7108",
    // Configure a client id for swagger requests.
    "ClientId": "452c617c-1ddf-40b7-adb3-d7a98c42abe3",
    // Configure a client secret for swagger requests.
    "ClientSecret": "G_-8Q~XxVfkV5Ni-0IRLJreSpXixlSZsIjhBYaK-"
  },
  // This clientid is used if endpoints invoked from swagger and identifies the management application for Entra (to get application roles)
  "Application": {
    "ClientId": "452c617c-1ddf-40b7-adb3-d7a98c42abe3"
  },
  // These settings are for the trust office
  "TrustOffice": {
    "EmailDomain": "netprogroup.com",
    "ClientPortalUrl": "https://clientfilings-dev.netprodevelopment.com",
    "ProductionOfficeEmailSuffix": "noreply",
    "InvitationWhiteList": [
      "*@netprogroup.com"
    ],
    "AllowedDomains": "netprogroup.com,netprogroupnv.onmicrosoft.com",
    "RecipientOverride": {
      "ProductionOffice": "<EMAIL>",
      "Invitation": "<EMAIL>",
      "Announcement": "<EMAIL>"
    },
    "SendInvitationToUserEnabled": false
  },
  "DataMigration": {
    "ProgressUpdateInterval": 1,
    "UseDummyInitialSync": false,
    "JobLockRefreshMarginSeconds": 290,
    "CountryOverrides": {
      "KNA": "St. Kitts and Nevis"
    }
  },
  "DataSync": {
    "Enabled": true,
    "JurisdictionCodes": [
      "KN" // Saint Kitts & Nevis
    ]
  },
  "SeedTestDataOnStartup": true,
  "GoLive": {
    "GoliveDate": "2025-1-1",
    "SendQueuedInvitations": false
  }
}