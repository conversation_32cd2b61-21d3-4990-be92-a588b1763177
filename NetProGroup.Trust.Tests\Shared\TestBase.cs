﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.DependencyInjection.Extensions;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Trust.Application.AppServices.Jurisdictions;
using NetProGroup.Trust.DataManager.AutoMapper;
using NetProGroup.Trust.DataManager.Bulk;
using NetProGroup.Trust.DataManager.Forms;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Payments.PaymentProvider;
using NetProGroup.Trust.Domain.Payments.Provider;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Import.Importers;
using NetProGroup.Trust.Payment.Provider;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Template;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Tests.Shared
{
    public abstract class TestBase
    {
        protected TestServer _server;
        protected HttpClient HttpClient;
        protected ApplicationUserDTO ManagementUser;
        protected ApplicationUserDTO ClientUser;
        protected MasterClient _masterClient;
        private Guid _superAdminRoleId = Guid.Parse("{F7B327DB-4633-484A-B8FF-63DEA19FAC02}");
        protected Guid ModuleStrId;
        protected Guid ModuleBfrId;
        protected Guid ModuleEsId;
        protected Guid ModuleEsBviId;
        protected Guid JurisdictionNevisId;
        protected Guid JurisdictionBahamasId;
        protected Jurisdiction JurisdictionNevis;
        protected Jurisdiction JurisdictionBahamas;
        protected bool SeedFormTemplates = true;
        protected readonly string _clientUserEmail = "<EMAIL>";

        [SetUp]
        public void SetupBase()
        {
            var config = new ConfigurationBuilder()
                .SetBasePath(TestContext.CurrentContext.TestDirectory)
                .AddJsonFile("appsettings.json", optional: true)
                .AddJsonFile("appsettings.Test.json", optional: false)
                .AddEnvironmentVariables()
                .AddUserSecrets<TestBase>()
                .Build();

            _server = new TestServer(
                new WebHostBuilder().UseEnvironment("Development")
                                    .UseConfiguration(config)
                                    .UseStartup<Startup>()
                                    .ConfigureTestServices((services) => ConfigureTestServices(services))
            );

            HttpClient = _server.CreateClient();

            OnSetup();
        }

        protected virtual void OnSetup()
        {
            var dbcontext = _server.Services.GetRequiredService<TrustDbContext>();
            dbcontext.Database.EnsureCreated();
            // dbcontext.Database.Migrate();

            Seed().Wait();
        }

        protected void SetWorkContextUser(ApplicationUserDTO user)
        {
            var workContext = _server.Services.GetRequiredService<IWorkContext>();

            workContext.IdentityUserId = user.Id;
            workContext.User = user;
        }

        protected virtual void ConfigureTestServices(IServiceCollection services)
        {
            services.AddApplicationInsightsTelemetry();

            // Register services from assemblies
            var assemblies = new List<System.Reflection.Assembly>
            {
                typeof(JurisdictionsAppService).Assembly,
                typeof(ApplicationProfile).Assembly,
                typeof(MasterClientImport).Assembly,
                typeof(IJurisdictionsRepository).Assembly,
                typeof(TrustDbContext).Assembly,
                typeof(IPaymentProvider).Assembly,
                typeof(IExcelTemplateService<>).Assembly
            };
            services.RegisterServices(assemblies.ToArray());

            // Register the SimpleBulkOperationProvider for tests
            services.AddSingleton<IBulkOperationProvider, SimpleBulkOperationProvider>();
            services.AddTransient<TestSeeder>();
            services.AddScoped<IWorkContext, TestWorkContext>();
        }

        private async Task Seed()
        {
            var jurisdictionRepository = _server.Services.GetRequiredService<IJurisdictionsRepository>();
            var jurisdictionNevis = (await jurisdictionRepository.FindByConditionAsync(j => j.Code == JurisdictionCodes.Nevis)).Single();
            var jurisdictionBahamas = (await jurisdictionRepository.FindByConditionAsync(j => j.Code == JurisdictionCodes.Bahamas)).Single();

            JurisdictionNevis = jurisdictionNevis;
            JurisdictionBahamas = jurisdictionBahamas;
            JurisdictionNevisId = jurisdictionNevis.Id;
            JurisdictionBahamasId = jurisdictionBahamas.Id;

            await SetupModulesAsync();

            if (SeedFormTemplates)
            {
                await CreateFormTemplatesAsync();
            }

            var seeder = _server.Services.GetRequiredService<TestSeeder>();
            await seeder.RunAsync();

            // Users
            var userManager = _server.Services.GetRequiredService<IUserManager>();

            userManager.CreateRoleAsync(new ApplicationRoleDTO
            {
                Id = _superAdminRoleId,
                Name = WellKnownRoleNames.Common_SuperAdmin,
                DisplayName = WellKnownRoleNames.Common_SuperAdmin
            }).Wait();

            userManager.CreateRoleAsync(new ApplicationRoleDTO
            {
                Id = WellKnownRoleIds.Client,
                Name = WellKnownRoleNames.Client,
                DisplayName = WellKnownRoleNames.Client
            }).Wait();

            ManagementUser = userManager.CreateUserWithRolesAsync(
                new RegistrationDTO
                {
                    LastName = "User 1",
                    FirstName = "Test",
                    UserName = "<EMAIL>",
                    DisplayName = "Test User 1",
                    ObjectId = Guid.NewGuid(),
                    RoleIds = new List<Guid> { Guid.Parse("{A00D4E6C-DCD6-4FCA-B080-666EEB9AE6CE}") }
                }
            ).Result;

            ClientUser = userManager.CreateUserWithRolesAsync(
                new RegistrationDTO
                {
                    LastName = "User 2",
                    Email = _clientUserEmail,
                    FirstName = "Test",
                    UserName = _clientUserEmail,
                    DisplayName = "Test User 2",
                    ObjectId = Guid.NewGuid(),
                    RoleIds = new List<Guid> { WellKnownRoleIds.Client }
                }
            ).Result;

            _masterClient = _server.Services.GetRequiredService<IMasterClientsRepository>().DbContext.Set<MasterClient>().Add(new MasterClient()
            {
                Code = "TEST_123"
            }).Entity;

            _masterClient.MasterClientUsers.Add(
                new MasterClientUser() { MasterClientId = _masterClient.Id, UserId = ClientUser.Id });

            //payments
            var payment = new Domain.Payments.Payment(new Guid())
            {
                LegalEntityId = new Guid("b4f2b7a4-7c0b-47e3-9a6a-935f5eae9f42"),
                CurrencyId = new Guid("3f8d5cba-3df4-49a7-a1ab-42c4e55f9b5c"),
                Amount = 1500.00M,
                Status = PaymentStatus.Pending,
                CreatedAt = new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                UpdatedAt = new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                ConcurrencyStamp = Guid.NewGuid(),
                LegalEntity = new LegalEntity
                {
                    Name = "Company LLC",
                    Code = "E-LL",
                    EntityType = LegalEntityType.Company,
                    IncorporationDate = DateTime.UtcNow.AddYears(-2),
                    JurisdictionId = new Guid("BDEF352D-DEDC-4271-888D-EFA168404CE9")
                },
                PaymentInvoices = new List<PaymentInvoice>
                {
                    new()
                    {
                        Invoice = new Invoice(Guid.NewGuid())
                        {
                            InvoiceNr = "*********",
                            Date = new DateTime(2023, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc),
                            FinancialYear = 2023,
                            Layout = LayoutConsts.TridentTrust
                        }
                    }
                }
            };

            // Payment Provider
            var paymentProviderService = _server.Services.GetRequiredService<IPaymentProviderRepository>();

            var paymentProvider = new PaymentProvider(Guid.NewGuid())
            {
                Name = "CXPAYKEY",
                Key = "CXPAYKEY",
                BaseUrl = "https://cxpay.transactiongateway.com/api/v2/three-step",
                ApiKey = "2F822Rw39fx762MaV7Yy86jXGTC7sCDy",
                ApiSecret = "2F822Rw39fx762MaV7Yy86jXGTC7sCDy",
                CreatedAt = DateTime.UtcNow, // SYSDATETIME()
                UpdatedAt = DateTime.UtcNow, // SYSDATETIME()
                ConcurrencyStamp = Guid.NewGuid() // NEWID()
            };

            paymentProviderService.Insert(paymentProvider);
            paymentProviderService.SaveChanges();

            // Payment Transaction
            var paymentTransactionService = _server.Services.GetRequiredService<IPaymentTransactionRepository>();

            var paymentTransaction = new PaymentTransaction(new Guid("78fcb820-4ba6-4383-a07e-a798658ad020"))
            {
                Payment = payment,
                PaymentProvider = paymentProvider,
                Result = "Pending", // Result of the transaction
                ResultCode = "100", // Assuming 100 represents 'In Progress'
                ResultMessage = "Transaction is currently in progress",
                TransactionId = "TXN*********0", // Unique transaction ID
                Status = "In Progress", // Transaction status
                CardDigits = "1234", // Last 4 digits of the card used
                ProcessCreatedAt = DateTime.UtcNow, // Date and time when the transaction was created
                PaidAt = null, // Set when payment is completed
                IsFinished = false, // Transaction is not yet finished
                FirstName = "John", // First name of the customer
                LastName = "Doe", // Last name of the customer
                Address = "123 Main St", // Customer address
                City = "New York", // Customer city
                State = "NY", // Customer state
                ZipCode = "10001", // Customer ZIP code
                Company = "Customer's Company", // Company name if applicable
                PhoneNumber = "555-1234", // Customer phone number
                PaymentProviderId = paymentProvider.Id,
                Email = "<EMAIL>", // Customer email
                CreatedAt = DateTime.UtcNow, // SYSDATETIME() equivalent for created timestamp
                UpdatedAt = DateTime.UtcNow, // SYSDATETIME() equivalent for updated timestamp
                ConcurrencyStamp = Guid.NewGuid() // NEWID() equivalent for concurrency control
            };

            // Insert the new PaymentTransaction into the repository
            paymentTransactionService.Insert(paymentTransaction);
            paymentTransactionService.SaveChanges();
        }

        /// <summary>
        /// Setup of all modules.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task SetupModulesAsync()
        {
            await CreateModuleAsync(ModuleKeyConsts.BODirectors, "BO/Directors");

            // Nevis
            ModuleStrId = await CreateModuleAsync(ModuleKeyConsts.SimplifiedTaxReturn, "Simplified Tax Return");

            // Panama
            ModuleBfrId = await CreateModuleAsync(ModuleKeyConsts.BasicFinancialReportPanama, "Basic Financial Report");

            // Bahamas
            ModuleEsId = await CreateModuleAsync(ModuleKeyConsts.EconomicSubstanceBahamas, "Economic Substance");

            // BVI
            ModuleEsBviId = await CreateModuleAsync(ModuleKeyConsts.EconomicSubstanceBVI, "Economic Substance");
        }

        /// <summary>
        /// Creates the form templates for the various years.
        /// </summary>
        /// <returns></returns>
        private async Task CreateFormTemplatesAsync()
        {
            var years = new int[] { 2019, 2020, 2021, 2022, 2023, 2024 };

            var formsDataManager = _server.Services.GetRequiredService<IFormsDataManager>();
            await formsDataManager.CreateFormTemplatesForJurisdictionAsync(JurisdictionNevis.Id, ModuleKeyConsts.SimplifiedTaxReturn, years);
            await formsDataManager.CreateFormTemplatesForJurisdictionAsync(JurisdictionBahamas.Id, ModuleKeyConsts.EconomicSubstanceBahamas, years);
        }

        /// <summary>
        /// Creates a module if it doesn't exist yet.
        /// </summary>
        /// <param name="key">The key for the module.</param>
        /// <param name="name">The name for the module.</param>
        /// <returns>The id of the module.</returns>
        private async Task<Guid> CreateModuleAsync(string key, string name)
        {
            var repository = _server.Services.GetRequiredService<IModulesRepository>();

            var existing = await repository.FindFirstOrDefaultByConditionAsync(x => x.Key == key);
            if (existing == null)
            {
                var toDb = new Module(Guid.NewGuid(), key, name);
                await repository.InsertAsync(toDb, saveChanges: true);
                return toDb.Id;
            }
            else
            {
                return existing.Id;
            }
        }
    }
}
