using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Models;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Tests.Shared;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.DataManager.LegalEntities.RequestResponses;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Shared.Enums;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Framework.Extensions;
using NetProGroup.Framework.Services.ActivityLogs.EFRepository;
using NetProGroup.Trust.DataManager.LegalEntities.Models;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Tests.LegalEntities
{
    [TestFixture]
    public class LegalEntitiesDataManagerTests : TestBase
    {
        private ILegalEntitiesRepository _legalEntitiesRepository;
        private ILegalEntitiesDataManager _legalEntitiesDataManager;
        private ISettingsManager _settingsManager;
        private IJurisdictionsRepository _jurisdictionsRepository;
        private IMasterClientsRepository _masterClientsRepository;
        private ILegalEntityHistoryRepository _legalEntityHistoryRepository;
        private List<Guid> _jurisdictionIDs;
        private IActivityLogRepository _activityLogRepository;

        [SetUp]
        public void Setup()
        {
            _legalEntitiesRepository = _server.Services.GetRequiredService<ILegalEntitiesRepository>();
            _legalEntitiesDataManager = _server.Services.GetRequiredService<ILegalEntitiesDataManager>();
            _jurisdictionsRepository = _server.Services.GetRequiredService<IJurisdictionsRepository>();
            _masterClientsRepository = _server.Services.GetRequiredService<IMasterClientsRepository>();
            _legalEntityHistoryRepository = _server.Services.GetRequiredService<ILegalEntityHistoryRepository>();
            _activityLogRepository = _server.Services.GetRequiredService<IActivityLogRepository>();
            _settingsManager = _server.Services.GetRequiredService<ISettingsManager>();
            _jurisdictionIDs = _jurisdictionsRepository.GetQueryable().Select(j => j.Id).ToList();
        }

        #region ListCompaniesAsync Tests
        [Test]
        public async Task ListCompaniesAsync_FiltersByOnboardingStatus()
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Company 1", "C1", "MC1", onboardingStatus: OnboardingStatus.Onboarding),
                CreateTestLegalEntity("Company 2", "C2", "MC2", onboardingStatus: OnboardingStatus.Approved),
                CreateTestLegalEntity("Company 3", "C3", "MC3", onboardingStatus: OnboardingStatus.Declined)
            };
            await SeedLegalEntitiesAsync(legalEntities);

            var request = new ListCompaniesRequest
            {
                OnboardingStatuses = new List<OnboardingStatus> { OnboardingStatus.Approved },
                PageNumber = 1,
                PageSize = 10,
                AuthorizedJurisdictionIDs = _jurisdictionsRepository.GetQueryable().Select(j => j.Id).ToList()
            };

            // Act
            var result = await _legalEntitiesDataManager.ListCompaniesAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.CompanyItems.Should().NotBeNull();
            result.CompanyItems.Should().HaveCount(1);
            var companyDTO = result.CompanyItems.Single();
            companyDTO.Name.Should().Be("Company 2");
            companyDTO.OnboardingStatus.Should().Be(OnboardingStatus.Approved);
        }

        [Test]
        public async Task ListCompaniesAsync_FiltersPaginatesAndReturnsCorrectResults()
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Alpha Company", "AC1", "MC1", "LC1", 2020),
                CreateTestLegalEntity("Beta Corporation", "BC1", "MC2", "LC2", 2021, "23456", "REF2"),
                CreateTestLegalEntity("Gamma Corporation", "GE1", "MC3", "LC3", 2022),
                CreateTestLegalEntity("Delta Corporation", "DI1", "MC4", "LC4", 2023)
            };
            await SeedLegalEntitiesAsync(legalEntities);

            var request = new ListCompaniesRequest
            {
                SearchTerm = "Corp",
                Active = true,
                PageNumber = 1,
                PageSize = 2,
                AuthorizedJurisdictionIDs = _jurisdictionsRepository.GetQueryable().Select(j => j.Id).ToList()
            };

            // Act
            var result = await _legalEntitiesDataManager.ListCompaniesAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.CompanyItems.Should().NotBeNull();
            result.CompanyItems.Should().HaveCount(2);

            var company = result.CompanyItems.First();
            company.Name.Should().Be("Beta Corporation");
            company.Code.Should().Be("BC1");
            company.LegacyCode.Should().Be("LC2");
            company.IncorporationNumber.Should().Be("23456");
            company.IncorporationDate.Year.Should().Be(2021);
            company.OnboardingStatus.Should().Be(OnboardingStatus.Onboarding);
            company.MasterClientCode.Should().Be("MC2");
            company.ReferralOffice.Should().Be("REF2");
            company.JurisdictionName.Should().Be("Nevis");
            company.IsActive.Should().BeTrue();

            result.CompanyItems.PageCount.Should().Be(2);
            result.CompanyItems.PageNumber.Should().Be(1);
            result.CompanyItems.PageSize.Should().Be(2);
            result.CompanyItems.TotalItemCount.Should().Be(3);
        }

        [Test]
        public async Task ListCompaniesAsync_FiltersByJurisdiction()
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Alpha Company", "AC1", "MC1", "LC1", 2020),
                CreateTestLegalEntity("Beta Corporation", "BC1", "MC2", "LC2", 2021, "23456", "REF2"),
                CreateTestLegalEntity("Gamma Corporation", "GE1", "MC3", "LC3", 2022),
                CreateTestLegalEntity("Delta Corporation", "DI1", "MC4", "LC4", 2023)
            };
            await SeedLegalEntitiesAsync(legalEntities);

            var emptyList = new List<Guid>();
            var request = new ListCompaniesRequest
            {
                SearchTerm = "Corp",
                Active = true,
                PageNumber = 1,
                PageSize = 2,
                AuthorizedJurisdictionIDs = emptyList
            };

            // Act
            var result = await _legalEntitiesDataManager.ListCompaniesAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.CompanyItems.Should().NotBeNull();
            result.CompanyItems.Should().HaveCount(0);
        }
        #endregion
        #region SearchCompaniesWithAnnualFeeStatus Tests
        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_EmptySearchTerm_ReturnsAllCompanies()
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Company 1", "C1", "MC1"),
                CreateTestLegalEntity("Company 2", "C2", "MC2")
            };
            await SeedLegalEntitiesAsync(legalEntities);

            var request = CreateSearchRequest(2023, false);

            // Act
            var result = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request);

            // Assert
            result.CompanyItems.Should().HaveCount(2);
            result.CompanyItems.Should().Contain(c => c.CompanyName == "Company 1");
            result.CompanyItems.Should().Contain(c => c.CompanyName == "Company 2");
        }

        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_FiltersByJurisdiction()
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Company 1", "C1", "MC1"),
                CreateTestLegalEntity("Company 2", "C2", "MC2")
            };
            await SeedLegalEntitiesAsync(legalEntities);

            var emptyList = new List<Guid>();
            var request = new SearchCompanyWithAnnualFeeStatusRequest()
            {
                IsPaid = false,
                FinancialYear = 2023,
                PageNumber = 1,
                PageSize = 10,
                AuthorizedJurisdictionIDs = emptyList
            };

            // Act
            var result = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request);

            // Assert
            result.CompanyItems.Should().HaveCount(0);
        }

        [Test]
        [TestCase("Company 1")]
        [TestCase("C01")]
        [TestCase("MC01")]
        [TestCase("LC1")]
        public async Task SearchCompaniesWithAnnualFeeStatus_WithSearchTerm_ReturnsFilteredCompanies(string searchTerm)
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Company 1", "C01", "MC01", "LC1"),
                CreateTestLegalEntity("Company 2", "C02", "MC02", "LC2")
            };
            await SeedLegalEntitiesAsync(legalEntities);

            var request = CreateSearchRequest(2023, false, searchTerm);

            // Act
            var result = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request);

            // Assert
            result.CompanyItems.Should().HaveCount(1);
            result.CompanyItems.Should().Contain(c => c.CompanyName == "Company 1");
        }

        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_WrongSearchTerm_ReturnsEmptyList()
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Company 1", "C1", "MC1"),
                CreateTestLegalEntity("Company 2", "C2", "MC2")
            };
            await SeedLegalEntitiesAsync(legalEntities);

            var request = CreateSearchRequest(2023, false, "MC3");

            // Act
            var result = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request);

            // Assert
            result.CompanyItems.Should().BeEmpty();
        }

        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_ChecksPagination()
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Company 1", "C1", "MC1"),
                CreateTestLegalEntity("Company 2", "C2", "MC2")
            };
            await SeedLegalEntitiesAsync(legalEntities);

            var request = CreateSearchRequest(2023, false, pageNumber: 2, pageSize: 1);

            // Act
            var result = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.CompanyItems.Should().HaveCount(1);
            result.CompanyItems.Should().ContainSingle(c => c.CompanyName == "Company 2");
        }

        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_ChecksSubmissionMapping()
        {
            // Arrange
            var legalEntity = CreateTestLegalEntity("Company 1", "C1", "MC1", "LC1");
            SetAnnualFeeStatus(legalEntity, 2023, true);
            var submission1 = AddTestSubmission(legalEntity, 2023, true);
            await SeedLegalEntitiesAsync([legalEntity]);

            var request = CreateSearchRequest(financialYear: 2023, isPaid: true);

            // Act
            var result = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request);

            // Assert
            var companyResult = result.CompanyItems.Should().HaveCount(1).And.Subject.Single();
            companyResult.CompanyName.Should().Be("Company 1");
            companyResult.IsPaid.Should().BeTrue();
            companyResult.MasterClientCode.Should().Be("MC1");
            companyResult.CompanyCode.Should().Be("C1");
            companyResult.CompanyId.Should().Be(legalEntity.Id);
            companyResult.CompanyLegacyCode.Should().Be("LC1");
            companyResult.DateSubmissionCreated.Should().Be(submission1.CreatedAt);
            companyResult.DateSubmissionSubmitted.Should().Be(submission1.SubmittedAt);
        }

        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_FiltersByFinancialYear()
        {
            // Arrange
            var legalEntity = CreateTestLegalEntity("Company 1", "C1", "MC1");
            SetAnnualFeeStatus(legalEntity, 2023, true);
            SetAnnualFeeStatus(legalEntity, 2024, true);
            await SeedLegalEntitiesAsync([legalEntity]);

            // Act 
            var request = CreateSearchRequest(financialYear: 2023, isPaid: true);

            // Assert
            var result = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request);
            result.CompanyItems.Should().HaveCount(1).And.Subject.Single().IsPaid.Should().BeTrue();
        }

        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_UsesSettingToFilter()
        {
            var legalEntity = CreateTestLegalEntity("Company 1", "C1", "MC1");
            await AddFirstSubmissionYearSetting(legalEntity, 2020);

            SetAnnualFeeStatus(legalEntity, 2019, true); // Before first submission year. Would never happen in real life, but just to test the logic.
            SetAnnualFeeStatus(legalEntity, 2020, true);
            SetAnnualFeeStatus(legalEntity, 2021, false);

            await SeedLegalEntitiesAsync([legalEntity]);

            // Act & Assert for 2019 (before first filing year, so exempt from payment)
            var request2019 = CreateSearchRequest(financialYear: 2019, isPaid: true);
            var result2019 = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request2019);
            result2019.CompanyItems.Should().HaveCount(0);

            // Act & Assert for 2020 (first filing year, so fee must be paid)
            var request2020 = CreateSearchRequest(financialYear: 2020, isPaid: true);
            var result2020 = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request2020);
            result2020.CompanyItems.Should().HaveCount(1);

            // Act & Assert for 2021 (after first filing year, so fee must be paid)
            var request2021 = CreateSearchRequest(financialYear: 2021, isPaid: false);
            var result2021 = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request2021);
            result2021.CompanyItems.Should().HaveCount(1);
        }

        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_UsesIncorporationDateToFilter()
        {
            var legalEntity = CreateTestLegalEntity("Company 1", "C1", "MC1", incorporationYear: 2020);

            SetAnnualFeeStatus(legalEntity, 2019, true); // Before incorporation year. Would never happen in real life, but just to test the logic.
            SetAnnualFeeStatus(legalEntity, 2020, true);
            SetAnnualFeeStatus(legalEntity, 2021, true);

            await SeedLegalEntitiesAsync([legalEntity]);

            // Act & Assert for 2019 (before first filing year, so exempt from payment)
            var request2019 = CreateSearchRequest(financialYear: 2019, isPaid: true);
            var result2019 = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request2019);
            result2019.CompanyItems.Should().HaveCount(0);

            // Act & Assert for 2020 (first filing year, so fee must be paid)
            var request2020 = CreateSearchRequest(financialYear: 2020, isPaid: true);
            var result2020 = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request2020);
            result2020.CompanyItems.Should().HaveCount(1);

            // Act & Assert for 2021 (after first filing year, so fee must be paid)
            var request2021 = CreateSearchRequest(financialYear: 2021, isPaid: true);
            var result2021 = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(request2021);
            result2021.CompanyItems.Should().HaveCount(1);
        }

        [Test]
        public async Task SearchCompaniesWithAnnualFeeStatus_FiltersByPaidStatus()
        {
            // Arrange
            var legalEntities = new List<LegalEntity>
            {
                CreateTestLegalEntity("Company 1", "C1", "MC1"),
                CreateTestLegalEntity("Company 2", "C2", "MC2"),
                CreateTestLegalEntity("Company 3", "C3", "MC3")
            };

            SetAnnualFeeStatus(legalEntities[0], 2023, true);
            SetAnnualFeeStatus(legalEntities[1], 2023, false);
            // Company 3 has no annual fee status for 2023

            await SeedLegalEntitiesAsync(legalEntities);

            // Act & Assert for paid = true
            var requestPaid = CreateSearchRequest(financialYear: 2023, isPaid: true);
            var resultPaid = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(requestPaid);

            resultPaid.CompanyItems.Should().HaveCount(1);
            resultPaid.CompanyItems.Should().Contain(c => c.CompanyName == "Company 1" && c.IsPaid);

            // Act & Assert for paid = false
            var requestUnpaid = CreateSearchRequest(financialYear: 2023, isPaid: false);
            var resultUnpaid = await _legalEntitiesDataManager.SearchCompaniesWithAnnualFeeStatusAsync(requestUnpaid);

            resultUnpaid.CompanyItems.Should().HaveCount(2);
            resultUnpaid.CompanyItems.Should().Contain(c => c.CompanyName == "Company 2" && !c.IsPaid);
            resultUnpaid.CompanyItems.Should().Contain(c => c.CompanyName == "Company 3" && !c.IsPaid);
        }
        #endregion
        #region UpdateCompaniesAnnualFeeStatusAsync Tests
        [Test]
        public async Task UpdateCompaniesAnnualFeeStatusAsync_ValidInput_UpdatesAllCompanies()
        {
            // Arrange
            var companies = new List<LegalEntity>
            {
                CreateTestLegalEntity("Company 1", "C1", "MC1"),
                CreateTestLegalEntity("Company 2", "C2", "MC2"),
                CreateTestLegalEntity("Company 3", "C3", "MC3")
            };
            await SeedLegalEntitiesAsync(companies);

            var companyIds = companies.Select(c => c.Id).ToList();

            // Act
            await _legalEntitiesDataManager.UpdateCompaniesAnnualFeeStatusAsync(companyIds, 2023, true, _jurisdictionIDs);

            // Assert
            var updatedCompanies = await _legalEntitiesRepository.FindByConditionAsync(c => companyIds.Contains(c.Id));
            foreach (var company in updatedCompanies)
            {
                company.AnnualFees.Should().HaveCount(1);
                var annualFee = company.AnnualFees.Single();
                annualFee.FinancialYear.Should().Be(2023);
                annualFee.IsPaid.Should().BeTrue();
            }
        }

        [Test]
        public async Task UpdateCompaniesAnnualFeeStatusAsync_SomeNonExistentCompanies_ThrowsException()
        {
            // Arrange
            var company = CreateTestLegalEntity("Test Company", "TC1", "MC1");
            await SeedLegalEntitiesAsync([company]);

            var companyIds = new List<Guid> { company.Id, Guid.NewGuid() };

            // Act & Assert
            await _legalEntitiesDataManager
                .Invoking(m => m.UpdateCompaniesAnnualFeeStatusAsync(companyIds, 2023, true, _jurisdictionIDs))
                .Should().ThrowAsync<EntityNotFoundException>();
        }

        [Test]
        public async Task UpdateCompaniesAnnualFeeStatusAsync_EmptyList_DoesNothing()
        {
            // Arrange
            var emptyList = new List<Guid>();

            // Act & Assert
            await _legalEntitiesDataManager
                .Invoking(m => m.UpdateCompaniesAnnualFeeStatusAsync(emptyList, 2023, true, _jurisdictionIDs))
                .Should().NotThrowAsync();
        }
        #endregion

        #region SyncLegalEntitiesAsync Tests
        #region Initial checks
        [Test]
        public async Task SyncLegalEntitiesAsync_WithDuplicateSyncLegalEntity_OnlyProcessesFirst()
        {
            // Arrange

            // Create two SyncLegalEntity objects with same Code but different Names
            var firstSyncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC004", "TEST004");
            firstSyncLegalEntity.Name = "First Company Name";

            var secondSyncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC004", "TEST004");
            secondSyncLegalEntity.Name = "Second Company Name";

            // Act
            await SyncLegalEntities(firstSyncLegalEntity, secondSyncLegalEntity);

            // Assert - Verify only one legal entity was created with the first entity's name
            var createdLegalEntities = await _legalEntitiesRepository.GetQueryable().Where(le => le.Code == "TEST004").ToListAsync();
            createdLegalEntities.Should().HaveCount(1);

            var createdLegalEntity = createdLegalEntities.Single();
            createdLegalEntity.Name.Should().Be("First Company Name", "only the first SyncLegalEntity should be processed");
            createdLegalEntity.Code.Should().Be("TEST004");

            // Assert - Verify only one legal entity history was created with the first entity's name
            var legalEntityHistories = await _legalEntityHistoryRepository.GetQueryable().Where(leh => leh.Code == "TEST004").ToListAsync();
            legalEntityHistories.Should().HaveCount(1);

            var legalEntityHistory = legalEntityHistories.Single();
            legalEntityHistory.Name.Should().Be("First Company Name", "only the first SyncLegalEntity should be processed");
            legalEntityHistory.Status.Should().Be(LegalEntityStatus.Initial);
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_WithUnknownEntityType_ThrowsConstraintException()
        {
            // Arrange
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC002", "TEST002", LegalEntityType.Unknown);

            var request = new SyncLegalEntitiesRequest
            {
                LegalEntities = new List<SyncLegalEntity> { syncLegalEntity }
            };

            // Act & Assert
            var exception = await _legalEntitiesDataManager
                                  .Invoking(m => m.SyncLegalEntitiesAsync(request))
                                  .Should().ThrowAsync<ConstraintException>();

            exception.WithMessage("The EntityType 'Unknown' is not valid");
        }

        #endregion

        private static void LegalEntityShouldBeMappedCorrectly(LegalEntity legalEntity, SyncLegalEntity syncLegalEntity)
        {
            legalEntity.Name.Should().Be(syncLegalEntity.Name);
            legalEntity.Code.Should().Be(syncLegalEntity.Code);
            legalEntity.EntityType.Should().Be(syncLegalEntity.EntityType);
            legalEntity.OnboardingStatus.Should().Be(OnboardingStatus.Onboarding);
            legalEntity.ReferralOffice.Should().Be(syncLegalEntity.ReferralOffice);
            legalEntity.LegacyCode.Should().Be(syncLegalEntity.LegacyCode);
            legalEntity.ExternalUniqueId.Should().Be(syncLegalEntity.UniqueId);
            legalEntity.EntityTypeName.Should().Be(syncLegalEntity.EntityTypeName);
            legalEntity.MasterClientCode.Should().Be(syncLegalEntity.MasterClientCode);
            // TODO all properties
        }

        #region New Entity: Creation with history
        [Test]
        [TestCase(LegalEntityStatusCodes.Active)]
        [TestCase(LegalEntityStatusCodes.Closing)]
        public async Task
            SyncLegalEntitiesAsync_New_SyncActive_CreatesInactiveWithOnboardingStatusAndInitialHistory(
                string newEntityStatus)
        {
            // Arrange
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC003", "TEST003", entityStatusCode: newEntityStatus);

            // Act
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify legal entity was created
            var createdLegalEntity = await _legalEntitiesRepository.GetQueryable().SingleOrDefaultAsync(le => le.Code == "TEST003");
            createdLegalEntity.Should().NotBeNull();
            createdLegalEntity.IsActive.Should().BeFalse();
            createdLegalEntity.OnboardingStatus.Should().Be(OnboardingStatus.Onboarding);
            LegalEntityShouldBeMappedCorrectly(createdLegalEntity, syncLegalEntity);

            // Assert - Verify legal entity history was created with Initial status
            var legalEntityHistory = await _legalEntityHistoryRepository.GetQueryable().SingleOrDefaultAsync(leh => leh.Code == "TEST003");
            legalEntityHistory.Should().NotBeNull();
            legalEntityHistory.Status.Should().Be(LegalEntityStatus.Initial);
            HistoryShouldBeCreatedCorrectly(legalEntityHistory, syncLegalEntity);
        }

        private static void HistoryShouldBeCreatedCorrectly(LegalEntityHistory legalEntityHistory,
            SyncLegalEntity syncLegalEntity)
        {
            legalEntityHistory.Name.Should().Be(syncLegalEntity.Name);
            legalEntityHistory.Code.Should().Be(syncLegalEntity.Code);
            legalEntityHistory.EntityType.Should().Be(syncLegalEntity.EntityType);
            // TODO all properties
        }

        [Test]
        [TestCase(LegalEntityStatusCodes.Closed)]
        [TestCase(LegalEntityStatusCodes.MarkedForDeletion)]
        public async Task
            SyncLegalEntitiesAsync_New_SyncInactive_CreatesInactiveWithOnboardingUnknownAndInitialHistory(
                string newEntityStatusCode)
        {
            // Arrange
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC005", "TEST005", entityStatusCode: newEntityStatusCode);

            // Act
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify legal entity was created
            var createdLegalEntity = await _legalEntitiesRepository.GetQueryable().SingleOrDefaultAsync(le => le.Code == "TEST005");
            createdLegalEntity.Should().NotBeNull();
            createdLegalEntity.IsActive.Should().BeFalse();
            createdLegalEntity.OnboardingStatus.Should().Be(OnboardingStatus.Unknown);
            createdLegalEntity.Name.Should().Be("Test Company");
            createdLegalEntity.Code.Should().Be("TEST005");

            // Assert - Verify legal entity history was created with Initial status
            var legalEntityHistory = await _legalEntityHistoryRepository.GetQueryable().SingleOrDefaultAsync(leh => leh.Code == "TEST005");
            legalEntityHistory.Should().NotBeNull();
            legalEntityHistory.Status.Should().Be(LegalEntityStatus.Initial);
            legalEntityHistory.Name.Should().Be("Test Company");
            legalEntityHistory.Code.Should().Be("TEST005");
        }
        #endregion

        #region Existing Entity: data updates and history creation
        [Test]
        [TestCase(LegalEntityStatusCodes.Closed)]
        [TestCase(LegalEntityStatusCodes.MarkedForDeletion)]
        public async Task
            SyncLegalEntitiesAsync_SyncInactive_ExistingInactive_NoChanges_DoesNotCreateAdditionalHistory(
                string newEntityStatusCode)
        {
            // Arrange - First sync to create the entity
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC006", "TEST006");

            // Act - First sync creates the entity
            await SyncLegalEntities(syncLegalEntity);

            // Verify entity was created
            var createdLegalEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST006");

            // Verify initial history was created
            var initialHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST006");
            initialHistoryCount.Should().Be(1, "initial history record should be created");

            // Capture state before second sync
            var initialUpdateTime = createdLegalEntity.UpdatedAt;

            syncLegalEntity.EntityStatusCode = newEntityStatusCode;

            // Act - Second sync with same data (no changes to the data)
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify legal entity was not changed
            var unchangedLegalEntity = await _legalEntitiesRepository.GetQueryable().SingleOrDefaultAsync(le => le.Code == "TEST006");
            unchangedLegalEntity.UpdatedAt.Should().Be(initialUpdateTime, "entity should not be updated when there are no changes");

            // Assert - Verify no additional legal entity history was added
            var finalHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST006");
            finalHistoryCount.Should().Be(initialHistoryCount, "no new history records should be added when there are no changes");
        }

        [Test]
        [TestCase(LegalEntityStatusCodes.Closed)]
        [TestCase(LegalEntityStatusCodes.MarkedForDeletion)]
        public async Task
            SyncLegalEntitiesAsync_SyncInactive_ExistingInactive_BasicChanged_DoesNotApplyChanges(
                string entityStatusCode)
        {
            // TODO this should fail after the changes in 1.1 of the sync
            // Arrange - First sync to create the entity
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC007", "TEST007");

            // Act - First sync creates the entity
            await SyncLegalEntities(syncLegalEntity);

            // Verify entity was created as inactive
            var createdLegalEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST007");
            createdLegalEntity.IsActive.Should().BeFalse();
            createdLegalEntity.OnboardingStatus.Should().Be(OnboardingStatus.Onboarding);

            // Verify initial history was created
            var initialHistories = await GetHistoriesForLegalEntity("TEST007");

            // Capture initial state
            var initialUpdateTime = createdLegalEntity.UpdatedAt;
            var initialName = createdLegalEntity.Name;
            var initialReferralOffice = createdLegalEntity.ReferralOffice;

            // Arrange - Modify sync entity with changes AND set to closed/marked for deletion
            syncLegalEntity.EntityStatusCode = entityStatusCode;
            syncLegalEntity.Name = "Changed Company Name";

            // Act - Second sync with changes but entity is inactive
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify legal entity was NOT changed despite having changes in sync data
            var unchangedLegalEntity = await _legalEntitiesRepository.GetQueryable().SingleOrDefaultAsync(le => le.Code == "TEST007");
            unchangedLegalEntity.UpdatedAt.Should().Be(initialUpdateTime, "entity should not be updated when inactive");
            unchangedLegalEntity.Name.Should().Be(initialName, "entity name should not change when inactive");
            unchangedLegalEntity.ReferralOffice.Should().Be(initialReferralOffice, "entity referral office should not change when inactive");

            // Assert - Verify no additional legal entity history was added
            var histories = await GetHistoriesForLegalEntity("TEST007");
            var newHistories = histories.Except(initialHistories);

            newHistories.Should().BeEmpty("there should be no new history records when entity is inactive");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_SyncInactive_ExistingActive_BasicChanged_UpdatesBasicAndCreatesUpdateReceivedHistory()
        {
            // Arrange - Create an active entity first
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC020", "TEST020");
            await SyncLegalEntities(syncLegalEntity);

            // Make the entity active
            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST020");
            createdEntity.SetActive(true);
            await _legalEntitiesRepository.SaveChangesAsync();

            // Create initial history
            var initialHistory = new LegalEntityHistory
            {
                JurisdictionId = createdEntity.JurisdictionId.Value,
                Code = syncLegalEntity.Code,
                Name = syncLegalEntity.Name,
                EntityType = syncLegalEntity.EntityType,
                Status = LegalEntityStatus.Initial
            };
            await _legalEntityHistoryRepository.InsertAsync(initialHistory);
            await _legalEntityHistoryRepository.SaveChangesAsync();

            var initialHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST020");

            // Arrange - Change to inactive status with basic data changes
            syncLegalEntity.EntityStatusCode = LegalEntityStatusCodes.Closed;
            syncLegalEntity.Name = "Updated Company Name";
            syncLegalEntity.ReferralOffice = "Updated Referral Office";

            // Act - Sync with inactive status and basic changes
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify entity was updated with new basic data
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST020");
            updatedEntity.Name.Should().Be("Updated Company Name", "entity should be updated with new name");
            updatedEntity.ReferralOffice.Should().Be("Updated Referral Office", "entity should be updated with new referral office");

            // Assert - Verify new history was created with UpdateReceived status
            var finalHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST020");
            finalHistoryCount.Should().Be(initialHistoryCount + 1, "new history record should be created for basic changes on active entity becoming inactive");

            var newHistory = await _legalEntityHistoryRepository.GetQueryable()
                .Where(leh => leh.Code == "TEST020")
                .OrderByDescending(leh => leh.ReceivedAt)
                .FirstAsync();
            newHistory.Status.Should().Be(LegalEntityStatus.UpdateReceived, "history status should be UpdateReceived for active entity with basic changes becoming inactive");
            newHistory.Name.Should().Be("Updated Company Name", "history should contain the updated name");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_SyncInactive_ExistingActive_IncorporationChanged_UpdatesIncorporationAndCreatesUpdateReceivedHistory()
        {
            // Arrange - Create an active entity first
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC021", "TEST021");
            await SyncLegalEntities(syncLegalEntity);

            // Make the entity active
            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST021");
            createdEntity.SetActive(true);
            await _legalEntitiesRepository.SaveChangesAsync();

            // Create initial history
            var initialHistory = new LegalEntityHistory
            {
                JurisdictionId = createdEntity.JurisdictionId.Value,
                Code = syncLegalEntity.Code,
                Name = syncLegalEntity.Name,
                EntityType = syncLegalEntity.EntityType,
                IncorporationNr = syncLegalEntity.IncorporationNr,
                IncorporationDate = syncLegalEntity.IncorporationDate,
                Status = LegalEntityStatus.Initial
            };
            await _legalEntityHistoryRepository.InsertAsync(initialHistory);
            await _legalEntityHistoryRepository.SaveChangesAsync();

            var initialHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST021");

            // Arrange - Change to inactive status with incorporation data changes
            syncLegalEntity.EntityStatusCode = LegalEntityStatusCodes.Closed;
            syncLegalEntity.IncorporationNr = "UPDATED789";
            syncLegalEntity.IncorporationDate = new DateTime(2024, 6, 15);

            // Act - Sync with inactive status and incorporation changes
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify entity was updated with new incorporation data
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST021");
            updatedEntity.IncorporationNr.Should().Be("UPDATED789", "entity should be updated with new incorporation number");
            updatedEntity.IncorporationDate.Should().Be(new DateTime(2024, 6, 15), "entity should be updated with new incorporation date");
            updatedEntity.IsActive.Should().BeFalse("entity should be deactivated");

            // Assert - Verify new history was created with UpdateReceived status
            var finalHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST021");
            finalHistoryCount.Should().Be(initialHistoryCount + 1, "new history record should be created for incorporation changes on active entity becoming inactive");

            var newHistory = await _legalEntityHistoryRepository.GetQueryable()
                .Where(leh => leh.Code == "TEST021")
                .OrderByDescending(leh => leh.ReceivedAt)
                .FirstAsync();
            newHistory.Status.Should().Be(LegalEntityStatus.UpdateReceived, "history status should be UpdateReceived for active entity with incorporation changes becoming inactive");
            newHistory.IncorporationNr.Should().Be("UPDATED789", "history should contain the updated incorporation number");
            newHistory.IncorporationDate.Should().Be(new DateTime(2024, 6, 15), "history should contain the updated incorporation date");
        }

        // TODO SyncLegalEntitiesAsync_SyncInactive_ExistingActive_NoChanges_DoesNotCreateAdditionalHistory()



        [Test] // TODO this fails probably
        public async Task SyncLegalEntitiesAsync_SyncActive_ExistingActive_BasicChanged_ExistingEntityNoHistory_CreatesInitialHistory()
        {
            // Arrange - Create entity without history
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC027", "TEST027");
            await SyncLegalEntities(syncLegalEntity);

            // Make entity active and clear history
            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST027");
            createdEntity.SetActive(true);
            await _legalEntitiesRepository.SaveChangesAsync();

            // Clear existing history
            var existingHistory = await _legalEntityHistoryRepository.GetQueryable().Where(leh => leh.Code == "TEST027").ToListAsync();
            foreach (var history in existingHistory)
            {
                await _legalEntityHistoryRepository.DeleteAsync(history);
            }
            await _legalEntityHistoryRepository.SaveChangesAsync();

            // Arrange - Change basic data
            syncLegalEntity.Name = "Changed Company Name";

            // Act - Sync with basic changes
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify Initial history was created (not Confirmed)
            var newHistory = await _legalEntityHistoryRepository.GetQueryable().SingleOrDefaultAsync(leh => leh.Code == "TEST027");
            newHistory.Should().NotBeNull("history should be created");
            newHistory.Status.Should().Be(LegalEntityStatus.Initial, "history status should be Initial when no existing history");
            newHistory.Name.Should().Be("Changed Company Name", "history should contain updated name");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_SyncActive_ExistingActive_IncorporationChanged_ExistingEntityNoHistory_CreatesInitialHistory()
        {
            // Arrange - Create entity without history
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC028", "TEST028");
            await SyncLegalEntities(syncLegalEntity);

            // Make entity active and clear history
            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST028");
            createdEntity.SetActive(true);
            await _legalEntitiesRepository.SaveChangesAsync();

            // Clear existing history
            var existingHistory = await _legalEntityHistoryRepository.GetQueryable().Where(leh => leh.Code == "TEST028").ToListAsync();
            foreach (var history in existingHistory)
            {
                await _legalEntityHistoryRepository.DeleteAsync(history);
            }
            await _legalEntityHistoryRepository.SaveChangesAsync();

            // Arrange - Change incorporation data
            syncLegalEntity.IncorporationNr = "CHANGED999";

            // Act - Sync with incorporation changes
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify Initial history was created (not UpdateReceived)
            var newHistory = await _legalEntityHistoryRepository.GetQueryable().SingleOrDefaultAsync(leh => leh.Code == "TEST028");
            newHistory.Should().NotBeNull("history should be created");
            newHistory.Status.Should().Be(LegalEntityStatus.Initial, "history status should be Initial when no existing history");
            newHistory.IncorporationNr.Should().Be("CHANGED999", "history should contain updated incorporation number");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_SyncActive_ExistingActive_BothBasicAndIncorporationChanged_UpdatesAndCreatesUpdateReceivedHistory()
        {
            // Arrange - Create an active entity first
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC011", "TEST011");
            await SyncLegalEntities(syncLegalEntity);

            // Make the entity active
            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST011");
            createdEntity.SetActive(true);
            await _legalEntitiesRepository.SaveChangesAsync();

            // Create initial history
            var initialHistory = new LegalEntityHistory
            {
                JurisdictionId = createdEntity.JurisdictionId.Value,
                Code = syncLegalEntity.Code,
                Name = syncLegalEntity.Name,
                EntityType = syncLegalEntity.EntityType,
                IncorporationNr = syncLegalEntity.IncorporationNr,
                Status = LegalEntityStatus.Initial
            };
            await _legalEntityHistoryRepository.InsertAsync(initialHistory);
            await _legalEntityHistoryRepository.SaveChangesAsync();

            var initialHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST011");

            // Arrange - Change both basic and incorporation data
            syncLegalEntity.Name = "Changed Company Name";
            syncLegalEntity.IncorporationNr = "CHANGED456";

            // Act - Sync with both types of changes
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify new history was created with UpdateReceived status (incorporation takes precedence)
            var finalHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST011");
            finalHistoryCount.Should().Be(initialHistoryCount + 1, "new history record should be created for changes");

            var newHistory = await _legalEntityHistoryRepository.GetQueryable()
                .Where(leh => leh.Code == "TEST011")
                .OrderByDescending(leh => leh.ReceivedAt)
                .FirstAsync();
            newHistory.Status.Should().Be(LegalEntityStatus.UpdateReceived, "history status should be UpdateReceived when incorporation changes (takes precedence over basic changes)");
            newHistory.Name.Should().Be("Changed Company Name", "history should contain the updated name");
            newHistory.IncorporationNr.Should().Be("CHANGED456", "history should contain the updated incorporation number");

            // Assert - Verify entity was updated with both changes
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST011");
            updatedEntity.Name.Should().Be("Changed Company Name", "entity should be updated with new name");
            updatedEntity.IncorporationNr.Should().Be("CHANGED456", "entity should be updated with new incorporation number");
        }


        [Test]
        public async Task SyncLegalEntitiesAsync_SyncActive_ExistingActive_IncorporationChanged_UpdatesIncorporationAndCreatesUpdateReceivedHistory()
        {
            // Arrange - Create an active entity first
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC010", "TEST010");
            await SyncLegalEntities(syncLegalEntity);

            // Make the entity active
            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST010");
            createdEntity.SetActive(true);
            await _legalEntitiesRepository.SaveChangesAsync();

            // Create initial history
            var initialHistory = new LegalEntityHistory
            {
                JurisdictionId = createdEntity.JurisdictionId.Value,
                Code = syncLegalEntity.Code,
                Name = syncLegalEntity.Name,
                EntityType = syncLegalEntity.EntityType,
                IncorporationNr = syncLegalEntity.IncorporationNr,
                Status = LegalEntityStatus.Initial
            };
            await _legalEntityHistoryRepository.InsertAsync(initialHistory);
            await _legalEntityHistoryRepository.SaveChangesAsync();

            var initialHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST010");

            // Arrange - Change incorporation data
            syncLegalEntity.IncorporationNr = "CHANGED123";

            // Act - Sync with incorporation changes
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify new history was created with UpdateReceived status
            var finalHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST010");
            finalHistoryCount.Should().Be(initialHistoryCount + 1, "new history record should be created for incorporation changes");

            var newHistory = await _legalEntityHistoryRepository.GetQueryable()
                .Where(leh => leh.Code == "TEST010")
                .OrderByDescending(leh => leh.ReceivedAt)
                .FirstAsync();
            newHistory.Status.Should().Be(LegalEntityStatus.UpdateReceived, "history status should be UpdateReceived for incorporation changes");
            newHistory.IncorporationNr.Should().Be("CHANGED123", "history should contain the updated incorporation number");

            // Assert - Verify entity was updated
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST010");
            updatedEntity.IncorporationNr.Should().Be("CHANGED123", "entity should be updated with new incorporation number");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_SyncActive_ExistingActive_BasicChanged_UpdatesBasicAndCreatesConfirmedHistory()
        {
            // Arrange - Create an active entity first
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC009", "TEST009");
            await SyncLegalEntities(syncLegalEntity);

            // Make the entity active
            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST009");
            createdEntity.SetActive(true);
            await _legalEntitiesRepository.SaveChangesAsync();

            // Create initial history
            var initialHistory = new LegalEntityHistory
            {
                JurisdictionId = createdEntity.JurisdictionId.Value,
                Code = syncLegalEntity.Code,
                Name = syncLegalEntity.Name,
                EntityType = syncLegalEntity.EntityType,
                Status = LegalEntityStatus.Initial
            };
            await _legalEntityHistoryRepository.InsertAsync(initialHistory);
            await _legalEntityHistoryRepository.SaveChangesAsync();

            var initialHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST009");

            // Arrange - Change basic data
            syncLegalEntity.Name = "Changed Company Name";

            // Act - Sync with basic changes
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify new history was created with Confirmed status
            var finalHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST009");
            finalHistoryCount.Should().Be(initialHistoryCount + 1, "new history record should be created for basic changes");

            var newHistory = await _legalEntityHistoryRepository.GetQueryable()
                .Where(leh => leh.Code == "TEST009")
                .OrderByDescending(leh => leh.ReceivedAt)
                .FirstAsync();
            newHistory.Status.Should().Be(LegalEntityStatus.Confirmed, "history status should be Confirmed for active entity basic changes");
            newHistory.Name.Should().Be("Changed Company Name", "history should contain the updated name");

            // Assert - Verify entity was updated
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST009");
            updatedEntity.Name.Should().Be("Changed Company Name", "entity should be updated with new name");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_SyncActive_ExistingActive_NoChanges_DoesNotCreateAdditionalHistory()
        {
            // Arrange - Create an active entity first
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC008", "TEST008");
            await SyncLegalEntities(syncLegalEntity);

            // Make the entity active
            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST008");
            createdEntity.SetActive(true);
            await _legalEntitiesRepository.SaveChangesAsync();

            // Create initial history
            var initialHistory = new LegalEntityHistory
            {
                JurisdictionId = createdEntity.JurisdictionId.Value,
                Code = syncLegalEntity.Code,
                Name = syncLegalEntity.Name,
                EntityType = syncLegalEntity.EntityType,
                Status = LegalEntityStatus.Initial
            };
            await _legalEntityHistoryRepository.InsertAsync(initialHistory);
            await _legalEntityHistoryRepository.SaveChangesAsync();

            var initialHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST008");
            var initialUpdateTime = createdEntity.UpdatedAt;

            // Act - Sync same data (no changes)
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify no additional history was created
            var finalHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST008");
            finalHistoryCount.Should().Be(initialHistoryCount, "no new history records should be added when there are no changes");

            // Assert - Verify entity was not updated
            var unchangedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST008");
            unchangedEntity.UpdatedAt.Should().Be(initialUpdateTime, "entity should not be updated when there are no changes");
        }



        [Test]
        public async Task SyncLegalEntitiesAsync_SyncActive_ExistingInactive_BasicChanged_CreatesUpdateReceivedHistory()
        {
            // Arrange - Create an inactive entity
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC020", "TEST020");
            await SyncLegalEntities(syncLegalEntity);

            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST020");
            createdEntity.IsActive.Should().BeFalse("entity should be created as inactive");

            // Create initial history
            var initialHistory = new LegalEntityHistory
            {
                JurisdictionId = createdEntity.JurisdictionId.Value,
                Code = syncLegalEntity.Code,
                Name = syncLegalEntity.Name,
                EntityType = syncLegalEntity.EntityType,
                Status = LegalEntityStatus.Initial
            };
            await _legalEntityHistoryRepository.InsertAsync(initialHistory);
            await _legalEntityHistoryRepository.SaveChangesAsync();

            var initialHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST020");

            // Arrange - Change to active status with basic changes
            syncLegalEntity.EntityStatusCode = LegalEntityStatusCodes.Active;
            syncLegalEntity.Name = "Updated Company Name";

            // Act - Sync with active status and basic changes
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify new history was created with UpdateReceived status
            var finalHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST020");
            finalHistoryCount.Should().Be(initialHistoryCount + 1, "new history record should be created for basic changes on inactive entity");

            var newHistory = await _legalEntityHistoryRepository.GetQueryable()
                .Where(leh => leh.Code == "TEST020")
                .OrderByDescending(leh => leh.ReceivedAt)
                .FirstAsync();
            newHistory.Status.Should().Be(LegalEntityStatus.UpdateReceived, "history status should be UpdateReceived for inactive entity changes");
            newHistory.Name.Should().Be("Updated Company Name", "history should contain the updated name");

            // Assert - Verify entity was updated
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST020");
            updatedEntity.Name.Should().Be("Updated Company Name", "entity should be updated with new name");
        }

        [Test] // TODO probably going to fail, and does this work as it should?
        public async Task SyncLegalEntitiesAsync_SyncActive_ExistingInactive_IncorporationChanged_CreatesUpdateReceivedHistory()
        {
            // Arrange - Create an inactive entity
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC021", "TEST021");
            await SyncLegalEntities(syncLegalEntity);

            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST021");
            createdEntity.IsActive.Should().BeFalse("entity should be created as inactive");

            // Create initial history
            var initialHistory = new LegalEntityHistory
            {
                JurisdictionId = createdEntity.JurisdictionId.Value,
                Code = syncLegalEntity.Code,
                Name = syncLegalEntity.Name,
                EntityType = syncLegalEntity.EntityType,
                IncorporationNr = syncLegalEntity.IncorporationNr,
                Status = LegalEntityStatus.Initial
            };
            await _legalEntityHistoryRepository.InsertAsync(initialHistory);
            await _legalEntityHistoryRepository.SaveChangesAsync();

            var initialHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST021");

            // Arrange - Change to active status with incorporation changes
            syncLegalEntity.EntityStatusCode = LegalEntityStatusCodes.Active;
            syncLegalEntity.IncorporationNr = "UPDATED789";

            // Act - Sync with active status and incorporation changes
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify new history was created with UpdateReceived status
            var finalHistoryCount = await _legalEntityHistoryRepository.GetQueryable().CountAsync(leh => leh.Code == "TEST021");
            finalHistoryCount.Should().Be(initialHistoryCount + 1, "new history record should be created for incorporation changes on inactive entity");

            var newHistory = await _legalEntityHistoryRepository.GetQueryable()
                .Where(leh => leh.Code == "TEST021")
                .OrderByDescending(leh => leh.ReceivedAt)
                .FirstAsync();
            newHistory.Status.Should().Be(LegalEntityStatus.UpdateReceived, "history status should be UpdateReceived for inactive entity incorporation changes");
            newHistory.IncorporationNr.Should().Be("UPDATED789", "history should contain the updated incorporation number");

            // Assert - Verify entity was updated
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST021");
            updatedEntity.IncorporationNr.Should().Be("UPDATED789", "entity should be updated with new incorporation number");
        }

        // TODO SyncLegalEntitiesAsync_SyncActive_ExistingInactive_BasicAndIncorporationChanged_CreatesUpdateReceivedHistory?()

        #endregion
        #region Status and OnboardingStatus transitions


        [Test]
        public async Task SyncLegalEntitiesAsync_SyncActive_ExistingInactiveApproved_ChangesToOnboarding()
        {
            // Arrange - Create an inactive entity with Approved status
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC015", "TEST015");
            await SyncLegalEntities(syncLegalEntity);

            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST015");
            createdEntity.OnboardingStatus = OnboardingStatus.Approved;
            await _legalEntitiesRepository.SaveChangesAsync();

            // Arrange - Change to active status
            syncLegalEntity.EntityStatusCode = LegalEntityStatusCodes.Active;

            // Act - Sync with active status
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify onboarding status changed to Onboarding
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST015");
            updatedEntity.OnboardingStatus.Should().Be(OnboardingStatus.Onboarding, "onboarding status should change from Approved to Onboarding when entity becomes active");
            updatedEntity.IsActive.Should().BeFalse("entity should remain inactive until manually activated");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_SyncActive_ExistingInactiveClosedWhileOnboarding_ChangesToOnboarding()
        {
            // Arrange - Create an inactive entity with ClosedWhileOnboarding status
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC016", "TEST016");
            await SyncLegalEntities(syncLegalEntity);

            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST016");
            createdEntity.OnboardingStatus = OnboardingStatus.ClosedWhileOnboarding;
            await _legalEntitiesRepository.SaveChangesAsync();

            // Arrange - Change to active status
            syncLegalEntity.EntityStatusCode = LegalEntityStatusCodes.Active;

            // Act - Sync with active status
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify onboarding status changed to Onboarding
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST016");
            updatedEntity.OnboardingStatus.Should().Be(OnboardingStatus.Onboarding, "onboarding status should change from ClosedWhileOnboarding to Onboarding when entity becomes active");
            updatedEntity.IsActive.Should().BeFalse("entity should remain inactive until manually activated");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_SyncActive_ExistingInactiveDeclined_ChangesToOnboarding()
        {
            // Arrange - Create an inactive entity with Declined status
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC017", "TEST017");
            await SyncLegalEntities(syncLegalEntity);

            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST017");
            createdEntity.OnboardingStatus = OnboardingStatus.Declined;
            await _legalEntitiesRepository.SaveChangesAsync();

            // Arrange - Change to active status
            syncLegalEntity.EntityStatusCode = LegalEntityStatusCodes.Active;

            // Act - Sync with active status
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify onboarding status changed to Onboarding
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST017");
            updatedEntity.OnboardingStatus.Should().Be(OnboardingStatus.Onboarding, "onboarding status should change from Declined to Onboarding when entity becomes active");
            updatedEntity.IsActive.Should().BeFalse("entity should remain inactive until manually activated");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_SyncActive_ExistingInactiveOnboarding_KeepsOnboardingStatus()
        {
            // Arrange - Create an inactive entity with Onboarding status
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC018", "TEST018");
            await SyncLegalEntities(syncLegalEntity);

            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST018");
            createdEntity.OnboardingStatus.Should().Be(OnboardingStatus.Onboarding, "entity should be created with Onboarding status");

            // Arrange - Change to active status
            syncLegalEntity.EntityStatusCode = LegalEntityStatusCodes.Active;

            // Act - Sync with active status
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify onboarding status remains Onboarding
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST018");
            updatedEntity.OnboardingStatus.Should().Be(OnboardingStatus.Onboarding, "onboarding status should remain Onboarding when already Onboarding");
            updatedEntity.IsActive.Should().BeFalse("entity should remain inactive until manually activated");
        }

        [Test] // TODO where is this in the diagram?
        public async Task SyncLegalEntitiesAsync_SyncActive_ExistingInactiveUnknown_KeepsUnknownStatus()
        {
            // Arrange - Create an inactive entity with Unknown status
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC019", "TEST019", entityStatusCode: LegalEntityStatusCodes.Closed);
            await SyncLegalEntities(syncLegalEntity);

            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST019");
            createdEntity.OnboardingStatus.Should().Be(OnboardingStatus.Unknown, "entity should be created with Unknown status for inactive sync");

            // Arrange - Change to active status
            syncLegalEntity.EntityStatusCode = LegalEntityStatusCodes.Active;

            // Act - Sync with active status
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify onboarding status remains Unknown
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST019");
            updatedEntity.OnboardingStatus.Should().Be(OnboardingStatus.Unknown, "onboarding status should remain Unknown when not in handled transition cases");
            updatedEntity.IsActive.Should().BeFalse("entity should remain inactive until manually activated");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_SyncInactive_ExistingActiveOnboarding_SetsClosedWhileOnboarding()
        {
            // Arrange - Create an active entity with Onboarding status
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC012", "TEST012");
            await SyncLegalEntities(syncLegalEntity);

            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST012");
            createdEntity.SetActive(true);
            createdEntity.OnboardingStatus = OnboardingStatus.Onboarding;
            await _legalEntitiesRepository.SaveChangesAsync();

            // Arrange - Change to inactive status
            syncLegalEntity.EntityStatusCode = LegalEntityStatusCodes.Closed;

            // Act - Sync with inactive status
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify entity is deactivated and onboarding status is set to ClosedWhileOnboarding
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST012");
            updatedEntity.IsActive.Should().BeFalse("entity should be deactivated");
            updatedEntity.OnboardingStatus.Should().Be(OnboardingStatus.ClosedWhileOnboarding, "onboarding status should be set to ClosedWhileOnboarding when active entity with Onboarding status becomes inactive");
        }

        [Test]
        [TestCase(LegalEntityStatusCodes.Closed)]
        [TestCase(LegalEntityStatusCodes.MarkedForDeletion)]
        public async Task SyncLegalEntitiesAsync_SyncInactive_ExistingActive_DeactivatesEntity(string newStatus)
        {
            // Arrange - Create an active entity with non-Onboarding status
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC013", "TEST013");
            await SyncLegalEntities(syncLegalEntity);

            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST013");
            createdEntity.ApproveOnboarding();
            await _legalEntitiesRepository.SaveChangesAsync();

            // Arrange - Change to inactive status
            syncLegalEntity.EntityStatusCode = newStatus;

            // Act - Sync with inactive status
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify entity is deactivated but onboarding status remains unchanged
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST013");
            updatedEntity.IsActive.Should().BeFalse("entity should be deactivated");
            updatedEntity.OnboardingStatus.Should().Be(OnboardingStatus.Approved, "onboarding status should remain unchanged when not Onboarding");
        }

        #endregion
        #region Master Client changes

        [Test]
        public async Task SyncLegalEntitiesAsync_SyncDifferentMasterClient_ExistingEntity_UpdatesMasterClientAndCreatesActivityLog()
        {
            // Arrange - Create entity with first master client
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC022", "TEST022");
            await SyncLegalEntities(syncLegalEntity);

            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST022");
            var originalMasterClientId = createdEntity.MasterClientId;

            // Arrange - Change to different master client
            syncLegalEntity.MasterClientCode = "MC023";
            syncLegalEntity.MasterClientName = "New Master Client";

            // Act - Sync with different master client
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify master client was updated
            var updatedEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST022");
            updatedEntity.MasterClientId.Should().NotBe(originalMasterClientId, "master client should be updated");
            updatedEntity.MasterClientCode.Should().Be("MC023", "master client code should be updated");

            // Assert - Verify new master client was created
            var newMasterClient = await _masterClientsRepository.GetQueryable().SingleOrDefaultAsync(mc => mc.Code == "MC023");
            newMasterClient.Should().NotBeNull("new master client should be created");
            newMasterClient.Name.Should().Be("New Master Client", "new master client should have correct name");
            newMasterClient.IsActive.Should().BeTrue("new master client should be active");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_NewMasterClient_CreatesActiveMasterClient()
        {
            // Arrange - Create sync entity with new master client code
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC024", "TEST024");

            // Verify master client doesn't exist
            var existingMasterClient = await _masterClientsRepository.GetQueryable().SingleOrDefaultAsync(mc => mc.Code == "MC024");
            existingMasterClient.Should().BeNull("master client should not exist initially");

            // Act - Sync entity with new master client
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify master client was created
            var createdMasterClient = await _masterClientsRepository.GetQueryable().SingleOrDefaultAsync(mc => mc.Code == "MC024");
            createdMasterClient.Should().NotBeNull("master client should be created");
            createdMasterClient.Code.Should().Be("MC024", "master client should have correct code");
            createdMasterClient.Name.Should().Be("Test Master Client", "master client should have correct name");
            createdMasterClient.IsActive.Should().BeTrue("master client should be active");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_ExistingMasterClientInactive_ReactivatesMasterClient()
        {
            // Arrange - Create inactive master client
            var inactiveMasterClient = new MasterClient
            {
                Code = "MC025",
                Name = "Inactive Master Client",
                IsActive = false
            };
            await _masterClientsRepository.InsertAsync(inactiveMasterClient);
            await _masterClientsRepository.SaveChangesAsync();

            // Arrange - Create sync entity with existing inactive master client
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC025", "TEST025");

            // Act - Sync entity with existing inactive master client
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify master client was reactivated
            var reactivatedMasterClient = await _masterClientsRepository.GetQueryable().SingleOrDefaultAsync(mc => mc.Code == "MC025");
            reactivatedMasterClient.Should().NotBeNull("master client should exist");
            reactivatedMasterClient.IsActive.Should().BeTrue("master client should be reactivated");
        }
        #endregion
        #region Error handling and process flow
        [Test]
        public async Task SyncLegalEntitiesAsync_EmptyRequest_DoesNothing()
        {
            // Arrange - Empty legal entities list
            var emptyRequest = new SyncLegalEntitiesRequest
            {
                LegalEntities = new List<SyncLegalEntity>()
            };

            // Act & Assert - Should not throw exception
            await _legalEntitiesDataManager
                .Invoking(m => m.SyncLegalEntitiesAsync(emptyRequest))
                .Should().NotThrowAsync("empty request should be handled gracefully");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_InvalidJurisdiction_ThrowsException()
        {
            // Arrange - Create sync entity with invalid jurisdiction
            var syncLegalEntity = CreateSyncLegalEntity("INVALID", "MC029", "TEST029");

            // Act & Assert - Should throw exception for invalid jurisdiction
            await _legalEntitiesDataManager
                .Invoking(m => m.SyncLegalEntitiesAsync(new SyncLegalEntitiesRequest { LegalEntities = new List<SyncLegalEntity> { syncLegalEntity } }))
                .Should().ThrowAsync<Exception>("invalid jurisdiction should cause an error");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_MultipleJurisdictions_ProcessesAllJurisdictions()
        {
            // Arrange - Create entities for different jurisdictions (assuming we have multiple jurisdictions)
            var nevisEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC031", "TEST031");

            // For this test, we'll use the same jurisdiction but different master clients to simulate processing
            var anotherEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC032", "TEST032"); // todo actually use different jurisdiction

            // Act - Sync entities from different contexts
            await SyncLegalEntities(nevisEntity, anotherEntity);

            // Assert - Both entities should be created
            var nevisCreated = await _legalEntitiesRepository.GetQueryable().SingleOrDefaultAsync(le => le.Code == "TEST031");
            var otherCreated = await _legalEntitiesRepository.GetQueryable().SingleOrDefaultAsync(le => le.Code == "TEST032");

            nevisCreated.Should().NotBeNull("Nevis entity should be created");
            otherCreated.Should().NotBeNull("Other entity should be created");
        }

        #endregion

        #region Activity Log and Bulk Operation Tests

        [Test]
        public async Task SyncLegalEntitiesAsync_NewEntity_CreatesLegalEntityAddedActivityLog()
        {
            // Arrange - Create new sync entity
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC033", "TEST033");

            var initialActivityLogCount = await GetActivityLogCountForEntity("TEST033");

            // Act - Sync new entity
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify activity log was created
            var finalActivityLogCount = await GetActivityLogCountForEntity("TEST033");
            finalActivityLogCount.Should().BeGreaterThan(initialActivityLogCount, "activity log should be created for new entity");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_OnboardingStatusChange_CreatesOnboardingChangedActivityLog()
        {
            // Arrange - Create inactive entity with Approved status
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC034", "TEST034");
            await SyncLegalEntities(syncLegalEntity);

            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST034");
            createdEntity.OnboardingStatus = OnboardingStatus.Approved;
            await _legalEntitiesRepository.SaveChangesAsync();

            var initialActivityLogCount = await GetActivityLogCountForEntity("TEST034");

            // Arrange - Change to active status (should trigger onboarding status change)
            syncLegalEntity.EntityStatusCode = LegalEntityStatusCodes.Active;

            // Act - Sync with active status
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify activity log was created for onboarding status change
            var finalActivityLogCount = await GetActivityLogCountForEntity("TEST034");
            finalActivityLogCount.Should().BeGreaterThan(initialActivityLogCount, "activity log should be created for onboarding status change");
        }

        [Test]
        public async Task SyncLegalEntitiesAsync_EntityDeactivation_CreatesDeactivatedActivityLog()
        {
            // Arrange - Create active entity
            var syncLegalEntity = CreateSyncLegalEntity(JurisdictionCodes.Nevis, "MC035", "TEST035");
            await SyncLegalEntities(syncLegalEntity);

            var createdEntity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == "TEST035");
            createdEntity.SetActive(true);
            await _legalEntitiesRepository.SaveChangesAsync();

            var initialActivityLogCount = await GetActivityLogCountForEntity("TEST035");

            // Arrange - Change to inactive status
            syncLegalEntity.EntityStatusCode = LegalEntityStatusCodes.Closed;

            // Act - Sync with inactive status
            await SyncLegalEntities(syncLegalEntity);

            // Assert - Verify activity log was created for deactivation
            var finalActivityLogCount = await GetActivityLogCountForEntity("TEST035");
            finalActivityLogCount.Should().BeGreaterThan(initialActivityLogCount, "activity log should be created for entity deactivation");
        }

        #endregion
        #endregion
        private async Task<int> GetActivityLogCountForEntity(string entityCode)
        {
            var entity = await _legalEntitiesRepository.GetQueryable().SingleAsync(le => le.Code == entityCode);
            var activityLogs = await _activityLogRepository.GetQueryable()
                .Where(al => al.EntityId == entity.Id)
                .ToListAsync();

            return activityLogs.Count;
        }

        private async Task SyncLegalEntities(params SyncLegalEntity[] syncLegalEntities)
        {
            // Act
            await _legalEntitiesDataManager.SyncLegalEntitiesAsync(new SyncLegalEntitiesRequest()
            {
                LegalEntities = syncLegalEntities.ToList()
            });
        }

        private async Task AddFirstSubmissionYearSetting(LegalEntity legalEntity, int financialYear)
        {
            await _settingsManager.SaveSettingsForCompanyAsync(new SubmissionSettingsDTO { FirstSubmissionYear = financialYear },
                legalEntity.Id);
        }

        private SyncLegalEntity CreateSyncLegalEntity(string jurisdictionCode, string masterClientCode, string entityCode,
            LegalEntityType entityType = LegalEntityType.Company, string entityStatusCode = LegalEntityStatusCodes.Active,
            string entityStatus = LegalEntityStatusNames.Active)
        {
            return new SyncLegalEntity
            {
                JurisdictionCode = jurisdictionCode,
                UniqueId = entityCode,
                EntityType = entityType,
                MasterClientCode = masterClientCode,
                MasterClientName = "Test Master Client",
                Code = entityCode,
                Name = "Test Company",
                IncorporationNr = "12345",
                LegacyCode = $"LEG{entityCode}",
                ReferralOffice = "REF1",
                EntityStatusCode = entityStatusCode,
                EntityStatus = entityStatus,
                EntityTypeCode = "CI",
                EntityTypeName = LegalEntityTypes.IBC
            };
        }

        private LegalEntity CreateTestLegalEntity(string name, string code, string masterClientCode, string legacyCode = null, int incorporationYear = 2000, string incorporationNr = "1234560", string referralOffice = "REF1", OnboardingStatus onboardingStatus = OnboardingStatus.Onboarding)
        {
            var jurisdiction = _jurisdictionsRepository.GetQueryable().First();

            return new LegalEntity(Guid.NewGuid())
            {
                Name = name,
                Code = code,
                LegacyCode = legacyCode,
                EntityType = LegalEntityType.Company,
                MasterClientCode = masterClientCode,
                MasterClient = new MasterClient { Code = masterClientCode },
                IncorporationDate = new DateTime(incorporationYear, 01, 01),
                IncorporationNr = incorporationNr,
                ReferralOffice = referralOffice,
                Jurisdiction = jurisdiction,
                OnboardingStatus = onboardingStatus,
                EntityTypeName = LegalEntityTypes.IBC
            };
        }

        private static void SetAnnualFeeStatus(LegalEntity legalEntity, int financialYear, bool isPaid)
        {
            legalEntity.UpdateAnnualFeeStatus(financialYear, isPaid);
        }

        private static Submission AddTestSubmission(LegalEntity legalEntity, int financialYear, bool isPaid)
        {
            var id = Guid.NewGuid();
            var submission = new Submission(id)
            {
                Name = $"Submission {financialYear}",
                FinancialYear = financialYear,
                CreatedAt = DateTime.UtcNow,
                SubmittedAt = DateTime.UtcNow.AddDays(1),
                ReportId = id.ToString(),
                IsPaid = isPaid,
                Layout = LayoutConsts.TridentTrust
            };

            legalEntity.Submissions.Add(submission);

            return submission;
        }

        private async Task SeedLegalEntitiesAsync(List<LegalEntity> legalEntities)
        {
            await _legalEntitiesRepository.InsertAsync(legalEntities);
            await _legalEntitiesRepository.SaveChangesAsync();
        }

        private SearchCompanyWithAnnualFeeStatusRequest CreateSearchRequest(int financialYear, bool isPaid,
            string searchTerm = "", int pageNumber = 1, int pageSize = 10)
        {
            return new SearchCompanyWithAnnualFeeStatusRequest
            {
                SearchTerm = searchTerm,
                FinancialYear = financialYear,
                PageNumber = pageNumber,
                PageSize = pageSize,
                IsPaid = isPaid,
                AuthorizedJurisdictionIDs = _jurisdictionsRepository.GetQueryable().Select(j => j.Id).ToList()
            };
        }
        
        private async Task<IEnumerable<LegalEntityHistory>> GetHistoriesForLegalEntity(string code)
        {
            return (await _legalEntityHistoryRepository.FindByConditionAsync(leh => leh.Code == code)).ToList();
        }
    }
}
