﻿// <copyright file="NevisSeeder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Trust.Application.Common;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.DataManager.Forms;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Payments.PaymentProvider;
using NetProGroup.Trust.Domain.Payments.Provider;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Application.Seeders.JurisdictionSeeders.Nevis
{
    /// <summary>
    /// Seeder for Nevis data.
    /// </summary>
    public class NevisSeeder : SeederBase, INevisSeeder
    {
        private readonly ILogger _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly ISettingsManager _settingsManager;
        private readonly AppSettings _appSettings;

        private Jurisdiction _jurisdiction;

        /// <summary>
        /// Initializes a new instance of the <see cref="NevisSeeder"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="serviceProvider">Instance of the serviceProvider.</param>
        /// <param name="settingsManager">Instance of the settings manager.</param>
        /// <param name="jurisdictionsRepository">Instance of the jurisdiction repository.</param>
        /// <param name="options">Instance of the app settings.</param>
        public NevisSeeder(ILogger<NevisSeeder> logger,
                           IServiceProvider serviceProvider,
                           ISettingsManager settingsManager,
                           IJurisdictionsRepository jurisdictionsRepository,
                           IOptions<AppSettings> options)
            : base(logger, serviceProvider)
        {
            ArgumentNullException.ThrowIfNull(options);

            _logger = logger;
            _serviceProvider = serviceProvider;
            _settingsManager = settingsManager;
            _jurisdictionsRepository = jurisdictionsRepository;
            _appSettings = options.Value;
        }

        /// <inheritdoc/>
        public async Task RunAsync()
        {
            _jurisdiction = await _jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(j => j.Code == JurisdictionCodes.Nevis);

            await CreateNevisFeesSettingsAsync();
            await CreateNevisLatePaymentFeesSettingsAsync();
            await CreateNevisInvoiceNumberingSettingsAsync();
            await CreateNevisInvoiceSettingsAsync();
            await CreateNevisFinancialSettingsAsync();
            await CreateNevisPaymentProviderAsync();

            await AssignModuleToJurisdictionAsync(ModuleKeyConsts.SimplifiedTaxReturn, JurisdictionCodes.Nevis);
            await AssignModuleToJurisdictionAsync(ModuleKeyConsts.BODirectors, JurisdictionCodes.Nevis);

            await CreateFormTemplatesAsync();

            if (_appSettings.SeedTestDataOnStartup)
            {
                var testDataSeeder = _serviceProvider.GetRequiredService<INevisTestDataSeeder>();
                await testDataSeeder.RunAsync();
            }
        }

        /// <summary>
        /// Create the settings for fees.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task CreateNevisFeesSettingsAsync()
        {
            var dirty = false;
            var feeSettings = await _settingsManager.ReadSettingsForJurisdictionAsync<FeeSettingsDTO>(_jurisdiction.Id);

            // STR
            if (!feeSettings.STRSubmissionFee.HasValue)
            {
                feeSettings.STRSubmissionFee = 100;
                dirty = true;
            }

            if (string.IsNullOrEmpty(feeSettings.STRSubmissionFeeInvoiceText))
            {
                feeSettings.STRSubmissionFeeInvoiceText = ConfigurationConsts.STR_INVOICETEXT;
                dirty = true;
            }

            // Save
            if (dirty)
            {
                await _settingsManager.SaveSettingsForJurisdictionAsync(feeSettings, _jurisdiction.Id);
            }
        }

        /// <summary>
        /// Create the settings for late payments.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task CreateNevisLatePaymentFeesSettingsAsync()
        {
            await SetNevisLatePaymentFeesSettingsAsync(2019, 75, null, null);
            await SetNevisLatePaymentFeesSettingsAsync(2020, 75, null, null);
            await SetNevisLatePaymentFeesSettingsAsync(2021, 75, null, null);
            await SetNevisLatePaymentFeesSettingsAsync(2022, 75, null, null);
            await SetNevisLatePaymentFeesSettingsAsync(2023, 75, null, null);
            await SetNevisLatePaymentFeesSettingsAsync(2024, 75, null, null);
        }

        /// <summary>
        /// Create the settings for late payments per year.
        /// </summary>
        /// <param name="year">The financial year to create the settings for.</param>
        /// <param name="amount">The amount for the fee.</param>
        /// <param name="start">The start of the period that this fee applies to.</param>
        /// <param name="end">The end of the period that this fee applies to.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task SetNevisLatePaymentFeesSettingsAsync(int year, decimal amount, DateTime? start, DateTime? end)
        {
            var latePaymentFees = await _settingsManager.GetSTRLatePaymentFeesForJurisdictionAsync(_jurisdiction.Id, year);
            if (latePaymentFees.Count == 0)
            {
                if (!start.HasValue)
                {
                    start = new DateTime(year + 1, 9, 2);
                }

                if (!end.HasValue)
                {
                    end = start.Value.AddYears(10);
                }

                var latePaymentFee = new STRLatePaymentFeeDTO
                {
                    FinancialYear = year,
                    Description = $"Late payment of fees for year {year}",
                    InvoiceText = $"Late filing fee",
                    Amount = amount,
                    CurrencyCode = "USD",
                    Charge = true,
                    StartAt = start.Value,
                    EndAt = end.Value
                };

                await _settingsManager.SaveSettingsForJurisdictionAsync(latePaymentFee, _jurisdiction.Id);
            }
        }

        /// <summary>
        /// Create the invoice settings.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task CreateNevisInvoiceSettingsAsync()
        {
            var dirty = false;
            var invoiceSettings = await _settingsManager.ReadSettingsForJurisdictionAsync<InvoiceSettingsDTO>(_jurisdiction.Id);

            // Save
            if (dirty)
            {
                await _settingsManager.SaveSettingsForJurisdictionAsync(invoiceSettings, _jurisdiction.Id);
            }
        }

        /// <summary>
        /// Create the settings for the numbering of the invoices.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task CreateNevisInvoiceNumberingSettingsAsync()
        {
            var dirty = false;

            var settings = await _settingsManager.GetInvoiceNumberingSettingsAsync(_jurisdiction.Id, null);

            if (string.IsNullOrEmpty(settings.PrefixFormat))
            {
                settings.PrefixFormat = "{yy}{mm}";
                dirty = true;
            }

            if (string.IsNullOrEmpty(settings.RangeFormat))
            {
                settings.RangeFormat = "{yy}{mm}";
                dirty = true;
            }

            if (string.IsNullOrEmpty(settings.FullInvoiceNumberFormat))
            {
                settings.FullInvoiceNumberFormat = "{prefix}/{number}";
                dirty = true;
            }

            if (settings.InitialNumber < 0)
            {
                settings.InitialNumber = 4000;
                dirty = true;
            }

            if (dirty)
            {
                await _settingsManager.SaveInvoiceNumberingSettingsAsync(_jurisdiction.Id, null, settings);
            }
        }

        /// <summary>
        /// Create the financial settings.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task CreateNevisFinancialSettingsAsync()
        {
            var dirty = false;
            var financialSettings = await _settingsManager.ReadSettingsForJurisdictionAsync<FinancialSettingsDTO>(_jurisdiction.Id);

            if (string.IsNullOrEmpty(financialSettings.CurrencyCode))
            {
                financialSettings.CurrencyCode = "USD";
                dirty = true;
            }

            // Save
            if (dirty)
            {
                await _settingsManager.SaveSettingsForJurisdictionAsync(financialSettings, _jurisdiction.Id);
            }
        }

        /// <summary>
        /// Create the payment provider.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task CreateNevisPaymentProviderAsync()
        {
            var dirty = false;
            var paymentProvider = _serviceProvider.GetRequiredService<IPaymentProviderRepository>();

            var paymentProviders = await paymentProvider.FindByConditionAsync(x => x.JurisdictionId == _jurisdiction.Id);

            var cxPay = paymentProviders.FirstOrDefault(pp => pp.Key == PaymentProviderKeys.CxPay);
            if (cxPay == null)
            {
                cxPay = new PaymentProvider(Guid.NewGuid())
                {
                    JurisdictionId = _jurisdiction.Id,
                    Key = PaymentProviderKeys.CxPay,
                    Name = "Nevis CxPay",
                    BaseUrl = "https://cxpay.transactiongateway.com/api/v2/three-step",
                    ApiKey = "2F822Rw39fx762MaV7Yy86jXGTC7sCDy",
                    ApiSecret = "2F822Rw39fx762MaV7Yy86jXGTC7sCDy"
                };
                await paymentProvider.InsertAsync(cxPay);

                dirty = true;
            }

            // Save
            if (dirty)
            {
                await paymentProvider.SaveChangesAsync();
            }
        }

        /// <summary>
        /// Creates the form templates for the various years.
        /// </summary>
        /// <returns></returns>
        private async Task CreateFormTemplatesAsync()
        {
            var years = new int[] { 2019, 2020, 2021, 2022, 2023, 2024 };

            var formsDataManager = _serviceProvider.GetRequiredService<IFormsDataManager>();
            await formsDataManager.CreateFormTemplatesForJurisdictionAsync(_jurisdiction.Id, ModuleKeyConsts.SimplifiedTaxReturn, years);
        }
    }
}
