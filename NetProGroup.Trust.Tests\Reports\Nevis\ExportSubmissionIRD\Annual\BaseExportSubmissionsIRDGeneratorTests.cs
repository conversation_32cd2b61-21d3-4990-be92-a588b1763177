using ClosedXML.Excel;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.Reports.Nevis.ExportSubmissionIRD.Annual
{
    /// <summary>
    /// Base test class for all IRD generator tests.
    /// Contains common setup and helper methods.
    /// </summary>
    public abstract class BaseExportSubmissionsIRDGeneratorTests<TGenerator> : TestBase
        where TGenerator : class, IExportSubmissionsIRDGenerator
    {
        protected TGenerator Generator;
        protected int FinancialYear;

        [SetUp]
        public virtual void Setup()
        {
            Generator = _server.Services.GetRequiredService<TGenerator>();

            // Get the client user
            var user = ClientUser;
            SetWorkContextUser(user);
        }

        private Dictionary<string, string> CreateDataSetWithEmptyTaxResidentCountry()
        {
            return new Dictionary<string, string>
            {
                // Tax resident information
                { "tax-resident.non-tax-resident", "false" },
                { "tax-resident.resident-country", "" }, // Empty country
                { "tax-resident.incorporated-before-2019", "false" },

                // Company information
                { "company.code", "TEST123" },
                { "company.name", "Test Company" },

                // Address information
                { "address-of-head-office.nevisAddress1", "123 Main St" },
                { "address-of-head-office.nevisAddress2", "Suite 100" },
                { "address-of-head-office.nevisAddress3", "" },
                { "address-of-head-office.nevisAddress4", "" },
                { "address-of-head-office.nevisAddress5", "" },

                // Add all other fields that might be needed
                { "company-details.companyType", "LLC" },
                { "company-details.incorporationDate", "2018-01-01" },
                { "company-details.registeredOffice", "Registered Office" },
                { "company-details.registeredAgent", "Registered Agent" },
                { "company-details.businessActivity", "Business Activity" },
                { "company-details.businessActivityDescription", "Business Activity Description" },

                // Business activities
                { "business-activities.activities.0.activity", "Trading" },
                { "business-activities.activities.0.description", "Trading Description" },
                { "business-activities.activities.0.primary", "true" },
                { "business-activities.activities.0.type", "Trading" },
                { "business-activities.activities.1.activity", "Consulting" },
                { "business-activities.activities.1.description", "Consulting Description" },
                { "business-activities.activities.1.primary", "false" },
                { "business-activities.activities.1.type", "Consulting" },

                // Additional fields that might be needed
                { "company-details.financialYearEnd", "12/31" },
                { "company-details.financialYearEndMonth", "12" },
                { "company-details.financialYearEndDay", "31" },
                { "company-details.email", "<EMAIL>" },
                { "company-details.phone", "************" },

                // Finalize section - use a date in the year after the financial year
                { "finalize.dateOfSignature", $"{FinancialYear + 1}-01-15" },
                { "finalize.signedBy", "John Doe" },
                { "finalize.position", "Director" }
            };
        }

        /// <summary>
        /// Common test method for testing that when the TaxResidentResidentCountry field is empty,
        /// the exported Excel file shows "No Country" in the appropriate cell.
        /// </summary>
        protected async Task TestEmptyTaxResidentCountryReturnsNoCountry(int row, int column)
        {
            // Arrange
            // Create a submission with an empty TaxResidentResidentCountry
            var submissionsManager = _server.Services.GetService<ISubmissionsManager>();

            var legalEntity = await _server.Services.GetRequiredService<ILegalEntitiesRepository>().InsertAsync(new LegalEntity()
            {
                Name = "Test Legal Entity",
                Code = "TEST_LEGAL_ENTITY",
                JurisdictionId = JurisdictionNevisId,
                MasterClientId = _masterClient.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                EntityTypeCode = LegalEntityTypes.IBC,
                EntityType = LegalEntityType.Company,
                EntityStatus = LegalEntityStatusNames.Active,
                ExternalUniqueId = "asdf",
                IncorporationNr = "1234",
                LegacyCode = "1234",
                IncorporationDate = DateTime.UtcNow.AddYears(-2),
                EntityTypeName = "IBC",
            }, true);

            var submission = await submissionsManager.StartSubmissionAsync(new StartSubmissionDTO()
            {
                FinancialYear = FinancialYear,
                ModuleId = ModuleStrId,
                LegalEntityId = legalEntity.Id
            });

            await submissionsManager.UpdateSubmissionDataSetAsync(new SubmissionDataSetDTO()
            {
                Id = submission.Id, DataSet = this.CreateDataSetWithEmptyTaxResidentCountry()
            });

            await submissionsManager.SubmitSubmissionAsync(new SubmitSubmissionDTO() { SubmissionId = submission.Id });
            
            // Act
            var result = Generator.ExportAsync(new ExportSubmissionDTO
            {
                SubmissionIds = [submission.Id],
                FinancialYear = FinancialYear
            }).Result;

            // Assert
            result.Should().NotBeNull();

            // Read the generated Excel file to verify the content
            using var resultWorkbook = new XLWorkbook(result.FileContent);
            var worksheet = resultWorkbook.Worksheet(1); // Submission tab

            // Find the cell with the country value
            var cellValue = worksheet.Cell(row, column).Value.ToString();

            // Verify that the value is "No Country"
            cellValue.Should().Be("No Country", $"Cell ({row}, {column}) should show 'No Country' for empty TaxResidentResidentCountry");
        }
    }
}
