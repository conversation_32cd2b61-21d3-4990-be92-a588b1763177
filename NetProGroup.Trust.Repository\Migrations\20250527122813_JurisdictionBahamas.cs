﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class JurisdictionBahamas : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // This was mistakenly already done with code in the seeder, so commenting it out to avoid duplicate key errors in future migrations.
            // TODO can be uncommented after this has been deployed to PRD
            //migrationBuilder.InsertData(
            //    table: "Jurisdictions",
            //    columns: new[] { "Id", "Code", "ConcurrencyStamp", "CreatedAt", "InitialSyncCompleted", "Name", "UpdatedAt" },
            //    values: new object[,]
            //    {
            //        {
            //            new Guid("2ca76629-b3e6-409d-b788-c63c802a4d4f"), "Bahamas", null, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), false, "Bahamas",
            //            new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
            //        },
            //        {
            //            new Guid("298175e2-a24f-4a7e-b7f3-a83e0447908c"), "Panama", null, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), false, "Panama",
            //            new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
            //        },
            //        {
            //            new Guid("ab54f8a6-dc29-4dd2-a39c-660bb980f789"), "BVI", null, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), false, "BVI",
            //            new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
            //        }
            //    });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Jurisdictions",
                keyColumn: "Id",
                keyValue: new Guid("2ca76629-b3e6-409d-b788-c63c802a4d4f"));
            migrationBuilder.DeleteData(
                table: "Jurisdictions",
                keyColumn: "Id",
                keyValue: new Guid("298175e2-a24f-4a7e-b7f3-a83e0447908c"));

            migrationBuilder.DeleteData(
                table: "Jurisdictions",
                keyColumn: "Id",
                keyValue: new Guid("ab54f8a6-dc29-4dd2-a39c-660bb980f789"));
        }
    }
}
