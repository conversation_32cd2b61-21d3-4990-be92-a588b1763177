{"ApplicationInsights": {"EnableAdaptiveSampling": false}, "Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Verbose", "Override": {"Microsoft": "Information", "Microsoft.AspNetCore": "Warning", "System": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Warning", "Microsoft.Extensions.Diagnostics.HealthChecks": "Debug", "Hangfire": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} [{SourceContext}] {NewLine}{Exception}", "restrictedToMinimumLevel": "Warning"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "AllowedHosts": "*", "DataMigration": {"ProgressUpdateInterval": 100, "UseNewBrandingLimitDate": "2023-11-08T00:00:00Z", "UseDummyInitialSync": false, "Enabled": true, "StoreUnprocessedRecords": true, "JobLockRefreshMarginSeconds": 120, "IgnorePaymentWhenNoInvoiceNumber": true}, "ShowPII": false}